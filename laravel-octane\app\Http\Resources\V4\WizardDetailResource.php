<?php

namespace App\Http\Resources\V4;



use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;

class WizardDetailResource extends JsonResource
{
    public function toArray($request)
    {
        $wizardInfo = $this->wizard_info;

        return [
            "id"              => $wizardInfo?->slug,
            "title"           => $this->wizard_detail_title,
            "subtitle"        => $this->wizard_detail_sub_title,
            "buttonText"      => $this->wizard_detail_button_text,
            "buttonLink"      => $this->wizard_detail_button_link,
            "image"           => $this->wizard_detail_images ? $this->uploaded_asset($this->wizard_detail_images) : null,
            "backgroundColor" => $this->wizard_detail_background_color,
            "mobileImage"     => $this->wizard_detail_mobile_images ? $this->uploaded_asset($this->wizard_detail_mobile_images) : null,
            "startDate"       => optional($wizardInfo)->start_date?->format('Y-m-d\TH:i:s\Z'),
            "endDate"         => optional($wizardInfo)->end_date?->format('Y-m-d\TH:i:s\Z'),
            "position"        => $this->wizard_detail_position ?? 0,
        ];
    }
    public function uploaded_asset($id)
    {
        $asset =  \App\Models\Upload::find($id);
        return $asset->file_name;// \Illuminate\Support\Facades\Storage::disk(env('FILESYSTEM_DRIVER'))->url($asset->file_name);

    }
}
