-- Sample database structure
-- This file demonstrates how to structure your SQL import files
-- You can replace this with your actual database dump

-- Ensure we're using the correct database
USE laravel;

-- Sample table creation (you can remove this if you have your own structure)
CREATE TABLE IF NOT EXISTS sample_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Sample data insertion (you can remove this if you have your own data)
INSERT INTO sample_data (name, email) VALUES 
('<PERSON>', '<EMAIL>'),
('<PERSON>', '<EMAIL>'),
('<PERSON>', '<EMAIL>')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- You can add more tables and data here
-- Remember to follow MySQL 8.0 syntax
