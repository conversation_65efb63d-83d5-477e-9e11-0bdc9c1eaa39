-- Create buzfi database
-- This script runs before importing the main database file

-- Create the buzfi database if it doesn't exist
CREATE DATABASE IF NOT EXISTS `buzfi` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the buzfi database
USE `buzfi`;

-- Grant all privileges to root user (already has them, but just to be explicit)
-- GRANT ALL PRIVILEGES ON buzfi.* TO 'root'@'%';

-- Grant privileges to laravel_user as well (backup user)
GRANT ALL PRIVILEGES ON buzfi.* TO 'laravel_user'@'%';

-- Flush privileges to ensure they take effect
FLUSH PRIVILEGES;

-- Display confirmation
SELECT 'buzfi database created and configured successfully' AS status;
