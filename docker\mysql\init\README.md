# Database Import Instructions

## How to Import SQL Files

1. **Place your SQL files in this directory** (`docker/mysql/init/`)
2. **Name your files with a numeric prefix** to control import order:

   - `01-your-database.sql`
   - `02-additional-data.sql`
   - etc.

3. **Restart the MySQL container** or rebuild the entire stack:
   ```bash
   docker-compose down
   docker-compose up -d
   ```

## Important Notes

- SQL files are imported **only when the MySQL container is first created**
- If you need to re-import, you must remove the MySQL volume:

  ```bash
  docker-compose down -v
  docker-compose up -d
  ```

- Files are imported in alphabetical order
- The import script will create the database if it doesn't exist
- All files should be compatible with MySQL 8.0

## Example SQL File Structure

Your SQL file should start with:

```sql
-- Create database if not exists
CREATE DATABASE IF NOT EXISTS buzfi;
USE buzfi;

-- Your table definitions and data here
CREATE TABLE example_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHA<PERSON>(255) NOT NULL
);

INSERT INTO example_table (name) VALUES ('Sample Data');
```

## Troubleshooting

- Check container logs: `docker-compose logs mysql`
- Verify file permissions (should be readable)
- Ensure SQL syntax is compatible with MySQL 8.0
- Check that file encoding is UTF-8
