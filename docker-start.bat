@echo off
echo Starting Laravel Docker Environment...
echo.

REM Check if <PERSON><PERSON> is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

REM Build and start containers
echo Building and starting containers...
docker-compose up -d --build

REM Wait a moment for containers to start
timeout /t 5 /nobreak >nul

REM Show container status
echo.
echo Container Status:
docker-compose ps

echo.
echo ================================
echo Laravel Docker Environment Ready!
echo ================================
echo.
echo Web Application: http://localhost:8080
echo phpMyAdmin:      http://localhost:8081
echo MySQL Port:      3306
echo.
echo To view logs: docker-compose logs -f
echo To stop:      docker-compose down
echo.
pause
