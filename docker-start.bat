@echo off
echo Starting Laravel Docker Environment...
echo.

REM Check if <PERSON><PERSON> is running
echo Checking Docker status...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Docker is not running. Please start Docker Desktop first.
    echo.
    echo Steps to fix:
    echo 1. Start Docker Desktop from Start menu
    echo 2. Wait for Docker to fully start (whale icon steady in system tray)
    echo 3. Run docker-check.bat for detailed diagnostics
    echo.
    pause
    exit /b 1
)

REM Check Docker Compose availability
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Using Docker Compose v2 syntax...
    set COMPOSE_CMD=docker compose
) else (
    echo Using Docker Compose v1 syntax...
    set COMPOSE_CMD=docker-compose
)

REM Build and start containers
echo Building and starting containers...
%COMPOSE_CMD% up -d --build

REM Wait a moment for containers to start
timeout /t 5 /nobreak >nul

REM Show container status
echo.
echo Container Status:
%COMPOSE_CMD% ps

echo.
echo ================================
echo Laravel Docker Environment Ready!
echo ================================
echo.
echo Web Application: http://localhost:8080
echo phpMyAdmin:      http://localhost:8081
echo MySQL Port:      3306
echo.
echo To view logs: %COMPOSE_CMD% logs -f
echo To stop:      %COMPOSE_CMD% down
echo.
pause
