# Laravel Docker Environment

This project is configured to run with Docker, including MySQL, phpMyAdmin, and Nginx.

## Quick Start

### Prerequisites
- Docker Desktop installed and running
- Git (optional, for version control)

### Starting the Environment

1. **Easy Start (Windows)**:
   ```bash
   docker-start.bat
   ```

2. **Manual Start**:
   ```bash
   docker-compose up -d --build
   ```

### Accessing the Application

- **Laravel Application**: http://localhost:8080
- **phpMyAdmin**: http://localhost:8081
  - Server: `mysql`
  - Username: `root`
  - Password: `root_password`
- **MySQL Direct Connection**:
  - Host: `localhost`
  - Port: `3306`
  - Database: `laravel`
  - Username: `laravel_user`
  - Password: `laravel_password`

## Services Included

### 1. Laravel Application (PHP 8.2 + FPM)
- **Container**: `laravel_app`
- **Port**: Internal 9000
- **Features**: PHP 8.2, <PERSON>, Node.js, Laravel Octane

### 2. Nginx Web Server
- **Container**: `laravel_nginx`
- **Port**: 8080 (HTTP), 443 (HTTPS)
- **Configuration**: Optimized for Laravel

### 3. MySQL Database
- **Container**: `laravel_mysql`
- **Port**: 3306
- **Version**: MySQL 8.0
- **Auto-import**: SQL files in `docker/mysql/init/`

### 4. phpMyAdmin
- **Container**: `laravel_phpmyadmin`
- **Port**: 8081
- **Features**: Full MySQL management interface

## Importing SQL Files

### Method 1: Automatic Import (Recommended)
1. Place your `.sql` files in `docker/mysql/init/`
2. Name them with numeric prefixes: `01-database.sql`, `02-data.sql`
3. Restart the environment:
   ```bash
   docker-compose down -v
   docker-compose up -d
   ```

### Method 2: Manual Import via phpMyAdmin
1. Access phpMyAdmin at http://localhost:8081
2. Login with root credentials
3. Import your SQL file through the interface

### Method 3: Command Line Import
```bash
docker exec -i laravel_mysql mysql -u root -proot_password laravel < your-file.sql
```

## Useful Commands

### Container Management
```bash
# Start environment
docker-compose up -d

# Stop environment
docker-compose down

# View logs
docker-compose logs -f

# Restart specific service
docker-compose restart app

# Access container shell
docker exec -it laravel_app bash
```

### Laravel Commands
```bash
# Run artisan commands
docker exec laravel_app php artisan migrate
docker exec laravel_app php artisan cache:clear
docker exec laravel_app php artisan config:clear

# Install Composer packages
docker exec laravel_app composer install

# Install NPM packages
docker exec laravel_app npm install
```

### Database Operations
```bash
# Access MySQL CLI
docker exec -it laravel_mysql mysql -u root -proot_password

# Backup database
docker exec laravel_mysql mysqldump -u root -proot_password laravel > backup.sql

# Import database
docker exec -i laravel_mysql mysql -u root -proot_password laravel < backup.sql
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**:
   - Change ports in `docker-compose.yml`
   - Stop conflicting services

2. **Permission Issues**:
   ```bash
   docker exec laravel_app chown -R www-data:www-data /var/www/html/storage
   ```

3. **Database Connection Failed**:
   - Ensure MySQL container is running
   - Check database credentials in `.env`

4. **Application Key Missing**:
   ```bash
   docker exec laravel_app php artisan key:generate
   ```

### Logs and Debugging
```bash
# View all logs
docker-compose logs

# View specific service logs
docker-compose logs mysql
docker-compose logs app
docker-compose logs nginx

# Follow logs in real-time
docker-compose logs -f app
```

## File Structure
```
├── docker/
│   ├── mysql/
│   │   ├── init/           # SQL files for auto-import
│   │   └── my.cnf          # MySQL configuration
│   ├── nginx/
│   │   └── default.conf    # Nginx configuration
│   ├── php/
│   │   └── local.ini       # PHP configuration
│   └── scripts/
│       └── start.sh        # Container startup script
├── laravel-octane/         # Laravel application
├── docker-compose.yml      # Docker services configuration
├── Dockerfile              # PHP container definition
└── README-Docker.md        # This file
```

## Environment Variables

The application uses `.env.docker` for Docker-specific configuration:
- Database host points to `mysql` container
- Application URL set to `http://localhost:8080`
- Debug mode enabled for development

## Security Notes

- Default passwords are for development only
- Change all passwords for production use
- Review and update security headers in Nginx config
- Consider using Docker secrets for sensitive data

## Support

For issues or questions:
1. Check container logs: `docker-compose logs`
2. Verify all containers are running: `docker-compose ps`
3. Ensure Docker Desktop is running and has sufficient resources
