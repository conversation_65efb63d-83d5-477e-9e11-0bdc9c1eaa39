#!/bin/bash

# This script will automatically import any .sql files found in the /docker-entrypoint-initdb.d directory
# Place your SQL dump files in the docker/mysql/init/ directory and they will be imported automatically

echo "Starting database import process..."

# Wait for MySQL to be ready
until mysql -u root -e "SELECT 1" >/dev/null 2>&1; do
    echo "Waiting for MySQL to be ready..."
    sleep 2
done

echo "MySQL is ready. Checking for SQL files to import..."

# Import any .sql files found in the directory
for sql_file in /docker-entrypoint-initdb.d/*.sql; do
    if [ -f "$sql_file" ]; then
        echo "Importing $sql_file..."
        mysql -u root "$MYSQL_DATABASE" < "$sql_file"
        if [ $? -eq 0 ]; then
            echo "Successfully imported $sql_file"
        else
            echo "Error importing $sql_file"
        fi
    fi
done

echo "Database import process completed."
