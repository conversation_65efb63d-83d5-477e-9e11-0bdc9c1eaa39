[mysqld]
# General settings
default-storage-engine=innodb
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# Connection settings
max_connections=200
max_allowed_packet=256M

# Buffer settings
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
innodb_flush_log_at_trx_commit=1
innodb_lock_wait_timeout=120

# Query cache
query_cache_type=1
query_cache_size=32M
query_cache_limit=2M

# Logging
general_log=1
general_log_file=/var/lib/mysql/general.log
slow_query_log=1
slow_query_log_file=/var/lib/mysql/slow.log
long_query_time=2

# Security
local_infile=0

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
