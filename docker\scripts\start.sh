#!/bin/bash

echo "Starting Laravel Docker container..."

# Wait for MySQL to be ready
echo "Waiting for MySQL to be ready..."
until php -r "
try {
    \$pdo = new PDO('mysql:host=mysql;port=3306', 'root', 'root_password');
    echo 'MySQL connection successful\n';
    exit(0);
} catch (Exception \$e) {
    exit(1);
}
"; do
    echo "Waiting for MySQL..."
    sleep 2
done
echo "MySQL is ready!"

# Copy Docker environment file if .env doesn't exist
if [ ! -f /var/www/html/.env ]; then
    echo "Copying Docker environment configuration..."
    cp /var/www/html/.env.docker /var/www/html/.env
fi

# Generate application key if not set
if ! grep -q "APP_KEY=base64:" /var/www/html/.env; then
    echo "Generating application key..."
    php artisan key:generate --force
fi

# Clear and cache configuration
echo "Clearing and caching configuration..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

# Run database migrations
echo "Running database migrations..."
php artisan migrate --force

# Create storage link
echo "Creating storage link..."
php artisan storage:link

# Set proper permissions
echo "Setting proper permissions..."
chown -R www-data:www-data /var/www/html/storage /var/www/html/bootstrap/cache
chmod -R 775 /var/www/html/storage /var/www/html/bootstrap/cache

echo "Laravel application is ready!"

# Start PHP-FPM
exec php-fpm
