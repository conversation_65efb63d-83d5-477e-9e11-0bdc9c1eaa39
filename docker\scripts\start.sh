#!/bin/bash

echo "Starting Laravel Docker container..."

# Wait for MySQL to be ready
echo "Waiting for MySQL to be ready..."
timeout=60
counter=0
until php -r "
try {
    \$pdo = new PDO('mysql:host=mysql;port=3306;dbname=buzfi', 'root', '', [
        PDO::ATTR_TIMEOUT => 5,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo 'MySQL connection successful\n';
    exit(0);
} catch (Exception \$e) {
    exit(1);
}
" 2>/dev/null; do
    counter=$((counter + 1))
    if [ $counter -gt $timeout ]; then
        echo "MySQL connection timeout after ${timeout} attempts. Starting PHP-FPM anyway..."
        break
    fi
    echo "Waiting for MySQL... (attempt $counter/$timeout)"
    sleep 1
done
echo "MySQL is ready!"

# Copy Docker environment file if .env doesn't exist
if [ ! -f /var/www/html/.env ]; then
    echo "Copying Docker environment configuration..."
    cp /var/www/html/.env.docker /var/www/html/.env
fi

# Generate application key if not set
if ! grep -q "APP_KEY=base64:" /var/www/html/.env; then
    echo "Generating application key..."
    php artisan key:generate --force
fi

# Clear and cache configuration
echo "Clearing and caching configuration..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

# Run database migrations
echo "Running database migrations..."
php artisan migrate --force

# Create storage link
echo "Creating storage link..."
php artisan storage:link

# Set proper permissions
echo "Setting proper permissions..."
chown -R www-data:www-data /var/www/html/storage /var/www/html/bootstrap/cache
chmod -R 775 /var/www/html/storage /var/www/html/bootstrap/cache

echo "Laravel application is ready!"

# Start PHP-FPM
exec php-fpm
