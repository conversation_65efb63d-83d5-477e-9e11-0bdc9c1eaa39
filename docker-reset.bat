@echo off
echo Resetting Laravel Docker Environment...
echo WARNING: This will remove all containers, volumes, and database data!
echo.
set /p confirm="Are you sure you want to continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Stopping containers...
docker-compose down

echo Removing volumes...
docker-compose down -v

echo Removing images...
docker-compose down --rmi all

echo Cleaning up Docker system...
docker system prune -f

echo.
echo Docker environment has been reset.
echo Run docker-start.bat to rebuild and start fresh.
echo.
pause
