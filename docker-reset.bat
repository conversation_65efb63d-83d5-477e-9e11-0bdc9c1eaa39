@echo off
echo Resetting Laravel Docker Environment...
echo WARNING: This will remove all containers, volumes, and database data!
echo.
set /p confirm="Are you sure you want to continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

REM Check Docker Compose availability
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    set COMPOSE_CMD=docker compose
) else (
    set COMPOSE_CMD=docker-compose
)

echo.
echo Stopping containers...
%COMPOSE_CMD% down

echo Removing volumes...
%COMPOSE_CMD% down -v

echo Removing images...
%COMPOSE_CMD% down --rmi all

echo Cleaning up Docker system...
docker system prune -f

echo.
echo Docker environment has been reset.
echo Run docker-start.bat to rebuild and start fresh.
echo.
pause
