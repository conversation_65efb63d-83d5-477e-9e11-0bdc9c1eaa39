<?php

use App\Http\Controllers\Api\V3\ApiHomePageController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/
Route::get('banners/hero',[ApiHomePageController::class,'hero_banners'])->name('api.v3.banners.hero');
Route::get('banners/hero2',[ApiHomePageController::class,'hero_banners2'])->name('api.v3.banners.hero2');
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
