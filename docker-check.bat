@echo off
echo Docker Environment Diagnostic
echo ===============================
echo.

echo Checking Docker installation...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not installed or not in PATH
    echo Please install Docker Desktop from: https://www.docker.com/products/docker-desktop
    goto :end
) else (
    echo [OK] Docker is installed
    docker --version
)

echo.
echo Checking Docker daemon status...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker daemon is not running
    echo.
    echo Solutions:
    echo 1. Start Docker Desktop from Start menu
    echo 2. Wait for Docker to fully start (whale icon steady in system tray)
    echo 3. Try running as Administrator
    echo 4. Restart Docker Desktop
    echo.
    goto :end
) else (
    echo [OK] Docker daemon is running
)

echo.
echo Checking Docker Compose...
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] docker-compose command not found, trying docker compose...
    docker compose version >nul 2>&1
    if %errorlevel% neq 0 (
        echo [ERROR] Docker Compose is not available
        goto :end
    ) else (
        echo [OK] Docker Compose (v2) is available
        docker compose version
    )
) else (
    echo [OK] Docker Compose (v1) is available
    docker-compose --version
)

echo.
echo Testing Docker functionality...
docker run --rm hello-world >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker cannot run containers
    echo This might be a permissions or virtualization issue
    goto :end
) else (
    echo [OK] Docker can run containers successfully
)

echo.
echo Checking required ports...
netstat -an | findstr ":8080" >nul 2>&1
if %errorlevel% equ 0 (
    echo [WARNING] Port 8080 is already in use
    echo You may need to stop other services or change the port in docker-compose.yml
)

netstat -an | findstr ":8081" >nul 2>&1
if %errorlevel% equ 0 (
    echo [WARNING] Port 8081 is already in use
    echo You may need to stop other services or change the port in docker-compose.yml
)

netstat -an | findstr ":3306" >nul 2>&1
if %errorlevel% equ 0 (
    echo [WARNING] Port 3306 is already in use
    echo You may need to stop MySQL/MariaDB or change the port in docker-compose.yml
)

echo.
echo ===============================
echo Docker Environment Check Complete
echo ===============================
echo.
echo If all checks passed, you can now run:
echo   docker-compose up -d --build
echo.

:end
pause
